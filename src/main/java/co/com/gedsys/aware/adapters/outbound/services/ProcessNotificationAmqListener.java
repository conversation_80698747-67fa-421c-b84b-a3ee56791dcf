package co.com.gedsys.aware.adapters.outbound.services;

import co.com.gedsys.commons.events.process.ProcesoCompletado;
import co.com.gedsys.aware.ports.models.*;
import co.com.gedsys.commons.constant.amqp.QueueName;

import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;

@Slf4j
@Service
@RabbitListener(queues = { QueueName.NOTIFICACIONES_PROCESOS })
public class ProcessNotificationAmqListener {
    private final NotificationServices notificationServices;
    private final SimpMessagingTemplate simpMessagingTemplate;

    public ProcessNotificationAmqListener(NotificationServices notificationServices,
            SimpMessagingTemplate simpMessagingTemplate) {
        this.notificationServices = notificationServices;
        this.simpMessagingTemplate = simpMessagingTemplate;
    }

    @RabbitHandler
    public void handleProcesoCompletado(ProcesoCompletado event) {
        log.debug("Processing ProcesoCompletado for process: {}", event.processName());

        if (event.stakeholders() == null) {
            log.debug("El proceso: {} no tiene stakeholders", event.processName());
            return;
        }

        if (!event.variables().containsKey("documentId")) {
            log.debug("El proceso: {} no tiene documentId", event.processName());
            return;
        }

        final var uniqueStakeholders = event.stakeholders().stream().distinct().toList();
        final var documentId = event.variables().get("documentId");

        for (String stakeholder : uniqueStakeholders) {
            var notification = Notification.builder()
                    .owner(stakeholder)
                    .title("Proceso completado: " + event.processName())
                    .type(NotificationType.success)
                    .action(new Action(ActionType.link,
                            String.format(UIPathConstants.VER_DOCUMENTO, documentId),
                            ActionText.VER_DOCUMENTO.name()))
                    .status(NotificationStatus.unread)
                    .timestamp(LocalDateTime.now())
                    .details(event)
                    .build();

            notificationServices.saveNotification(notification);
            simpMessagingTemplate.convertAndSend("/queue/" + stakeholder + "/notifications", notification);
        }
    }
} 