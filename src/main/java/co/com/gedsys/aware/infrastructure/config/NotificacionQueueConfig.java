package co.com.gedsys.aware.infrastructure.config;

import static co.com.gedsys.commons.constant.amqp.DeadLetterConstant.DLX;

import org.springframework.amqp.core.*;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.DependsOn;

import co.com.gedsys.commons.constant.amqp.QueueName;
import co.com.gedsys.commons.constant.amqp.RoutingKeyName;
import co.com.gedsys.commons.constant.amqp.TaskRoutingKeys;

/**
 * Configuración de colas y bindings para el manejo de notificaciones
 */
@Configuration
public class NotificacionQueueConfig {

    private static final String NOTIFICACIONES_DLQ = "notificaciones.dlq";
    private static final String NOTIFICACIONES_DLK = "notificaciones.dlk";

    // Colas de notificaciones
    @Bean
    Queue notificacionesDeadLetterQueue() {
        return QueueBuilder.durable(NOTIFICACIONES_DLQ)
                .withArgument("x-queue-type", "classic")
                .build();
    }

    @Bean
    Queue notificacionTareasQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES_TAREAS)
                .withArgument("x-queue-type", "classic")
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(NOTIFICACIONES_DLK)
                .build();
    }

    @Bean
    Queue notificacionProcesosQueue() {
        return QueueBuilder.durable(QueueName.NOTIFICACIONES_PROCESOS)
                .withArgument("x-queue-type", "classic")
                .deadLetterExchange(DLX)
                .deadLetterRoutingKey(NOTIFICACIONES_DLK)
                .build();
    }

    // Bindings
    @Bean
    @DependsOn({ "deadLetterExchange", "notificacionesDeadLetterQueue" })
    Binding notificacionesDeadLetterBinding(Queue notificacionesDeadLetterQueue,
            TopicExchange deadLetterExchange) {
        return BindingBuilder.bind(notificacionesDeadLetterQueue)
                .to(deadLetterExchange)
                .with(NOTIFICACIONES_DLK);
    }


    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionTareasQueue" })
    Binding notificacionTareasBinding(Queue notificacionTareasQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionTareasQueue)
                .to(mainTopicExchange)
                .with(RoutingKeyName.NOTIFICACION_NUEVA);
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionTareasQueue" })
    Binding tareaAsignadaBinding(Queue notificacionTareasQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionTareasQueue)
                .to(mainTopicExchange)
                .with(TaskRoutingKeys.TAREA_ASIGNADA);
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionTareasQueue" })
    Binding tareaCompletadaBinding(Queue notificacionTareasQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionTareasQueue)
                .to(mainTopicExchange)
                .with(TaskRoutingKeys.TAREA_COMPLETADA);
    }

    @Bean
    @DependsOn({ "mainTopicExchange", "notificacionProcesosQueue" })
    Binding notificacionProcesosBinding(Queue notificacionProcesosQueue,
            TopicExchange mainTopicExchange) {
        return BindingBuilder.bind(notificacionProcesosQueue)
                .to(mainTopicExchange)
                .with(RoutingKeyName.PROCESO_COMPLETADO);
    }

}
